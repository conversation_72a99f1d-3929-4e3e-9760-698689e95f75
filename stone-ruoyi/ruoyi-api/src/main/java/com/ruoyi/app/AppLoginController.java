package com.ruoyi.app;


import com.ruoyi.common.constant.Constants;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.common.utils.msg.SendMsgUtils;
import com.ruoyi.framework.web.service.TokenService;
import com.ruoyi.system.domain.AppUser;
import com.ruoyi.system.domain.vo.LoginUserVo;
import com.ruoyi.system.service.IAppUserService;
import com.wechat.pay.java.core.http.HttpRequest;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.HashMap;

/**
 * 登录验证
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/api")
public class AppLoginController {

    @Autowired
    private TokenService tokenService;

    @Autowired
    private IAppUserService appUserService;


    @Autowired
    private SendMsgUtils sendMsgUtils;


    /**
     * 登录方法
     *
     * @param map 登录信息
     * @return 结果
     */
    @PostMapping("/login")
    public AjaxResult login(@RequestBody LoginUserVo map ) {
        AjaxResult ajax = AjaxResult.success();
        // 设置一下登录时间
        // 无论是手机号校验，还是账号密码校验，如果为true，走底下的createToken方法
        HashMap res = appUserService.login(map);;
        if ((boolean)res.get("isLogin") == true) {
            LoginUser loginBody = (LoginUser) res.get("loginBody");
            loginBody.setLoginTime(new Date().getTime());
            String token = tokenService.createToken(loginBody);
            ajax.put(Constants.TOKEN, token);
            ajax.put("appUser", res.get("appUser"));
            return ajax;
        } else {
            return AjaxResult.error((String) res.get("msg"));
        }
    }

    /**
     * 普通注册
     * @return 结果
     */
    @PostMapping("/register")
    public AjaxResult register(@RequestBody LoginUserVo map) {
        AjaxResult ajax = AjaxResult.success();
        // 设置一下登录时间
        // 无论是手机号校验，还是账号密码校验，如果为true，走底下的createToken方法
        HashMap res = appUserService.register(map);;
        if ((boolean)res.get("isLogin") == true) {
            LoginUser loginBody = (LoginUser) res.get("loginBody");
            loginBody.setLoginTime(new Date().getTime());
            String token = tokenService.createToken(loginBody);
            ajax.put(Constants.TOKEN, token);
            ajax.put("appUser", res.get("appUser"));
            return ajax;
        } else {
            return AjaxResult.error((String) res.get("msg"));
        }
    }
    /**
     * 推荐人普通注册
     * @return 结果
     */
    @PostMapping("/referrerRegister")
    public AjaxResult referrerRegister(@RequestBody LoginUserVo map) {
        AjaxResult ajax = AjaxResult.success();
        // 设置一下登录时间
        // 无论是手机号校验，还是账号密码校验，如果为true，走底下的createToken方法
        HashMap res = appUserService.referrerRegister(map);;
        if ((boolean)res.get("isLogin") == true) {
            LoginUser loginBody = (LoginUser) res.get("loginBody");
            loginBody.setLoginTime(new Date().getTime());
            String token = tokenService.createToken(loginBody);
            ajax.put(Constants.TOKEN, token);
            ajax.put("appUser", res.get("appUser"));
            return ajax;
        } else {
            return AjaxResult.error((String) res.get("msg"));
        }
    }

    /**
     * 发送短信
     *
     * @param phone 手机号
     * @return 结果
     */
    @GetMapping("/sendMsg")
    public AjaxResult sendMsg(@RequestParam String phone) {
        String msg = sendMsgUtils.sendVerificationCode(phone);
        if("请稍后再试，冷却时间未到".equals(msg)) {
            return AjaxResult.error(msg);
        }
        return AjaxResult.success();
    }

    /**
     * 修改密码和交易密码
     * @param map
     * @return
     */
    @PostMapping("/updatePassword")
    public AjaxResult updatePassword(@RequestBody LoginUserVo map) {
        if(ObjectUtils.isEmpty(map) || StringUtils.isEmpty(map.getOldTransactionPassword())
        || StringUtils.isEmpty(map.getTransactionPassword())){
            return AjaxResult.error("交易密码或登录密码为空");
        }
        AjaxResult res = appUserService.updatePassword(map);;
        return res;

    }

    /**
     * 交易密码重设
     * @param map
     * @return
     */
    @PostMapping("/transactionPassword")
    public AjaxResult updateTransactionPassword(@RequestBody LoginUserVo map){
        AjaxResult ajax = AjaxResult.success();
        if(ObjectUtils.isEmpty(map) || StringUtils.isEmpty(map.getTransactionPassword())
        || StringUtils.isEmpty(map.getPhone()) || StringUtils.isEmpty(map.getPhoneCode())){
            return AjaxResult.error("请输入交易密码或者手机号");
        }
        HashMap res =appUserService.updateTransactionPassword(map);
        if ((boolean)res.get("isLogin") == true) {
            LoginUser loginBody = (LoginUser) res.get("loginBody");
            loginBody.setLoginTime(new Date().getTime());
            String token = tokenService.createToken(loginBody);
            ajax.put(Constants.TOKEN, token);
            ajax.put("appUser", res.get("appUser"));
            return ajax;
        } else {
            return AjaxResult.error((String) res.get("msg"));
        }
    }


    @GetMapping("/user/{userId}")
    public AjaxResult getUserByUserId(@PathVariable Integer userId) {
        AppUser user = appUserService.getUserByUserId(userId);
        return AjaxResult.success(user);
    }

    /**
     * 收款管理确认
     */
    @PostMapping("/getConfirm")
    public AjaxResult getConfirm (@RequestBody LoginUserVo map){
        AjaxResult ajax = AjaxResult.success();
        HashMap res =appUserService.getConfirm(map);
        if ((boolean)res.get("isLogin") == true) {
//            LoginUser loginBody = (LoginUser) res.get("loginBody");
//            loginBody.setLoginTime(new Date().getTime());
//            String token = tokenService.createToken(loginBody);
//            ajax.put(Constants.TOKEN, token);
//            ajax.put("appUser", res.get("appUser"));
            return ajax;
        } else {
            return AjaxResult.error((String) res.get("msg"));
        }
    }

    @PostMapping("/update")
    public AjaxResult updateById (@RequestBody AppUser appUser) {
        return AjaxResult.success(appUserService.updateAppUser(appUser));
    }

    @GetMapping("/get")
    public AjaxResult get (@RequestParam("id") Long id) {
        return AjaxResult.success(appUserService.selectAppUserById(id));
    }


}
