package com.ruoyi.api.controller;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.system.domain.HtAddress;
import com.ruoyi.system.service.IHtAddressService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 地址Controller
 *
 * <AUTHOR>
 * @date 2024-11-21
 */
@RestController
@RequestMapping("/api/address")
public class ApiHtAddressController extends BaseController {
    @Autowired
    private IHtAddressService htAddressService;

    /**
     * 查询地址列表
     */
    @Anonymous
    @GetMapping("/list")
    public AjaxResult list(HtAddress htAddress) {
        startPage();
        List<HtAddress> list = htAddressService.selectHtAddressList(htAddress);
        return AjaxResult.success(list);
    }


    /**
     * 获取地址详细信息
     */
    @Anonymous
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(htAddressService.selectHtAddressById(id));
    }

    /**
     * 新增地址
     */
    @Anonymous
    @PostMapping("/insertAddress")
    public AjaxResult add(@RequestBody HtAddress htAddress) {
        return toAjax(htAddressService.insertHtAddress(htAddress));
    }

    /**
     * 修改地址
     */
    @Anonymous
    @PostMapping("/updateAddress")
    public AjaxResult edit(@RequestBody HtAddress htAddress) {
        return toAjax(htAddressService.updateHtAddress(htAddress));
    }

    /**
     * 删除地址
     */
    @Anonymous
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(htAddressService.deleteHtAddressByIds(ids));
    }
}
