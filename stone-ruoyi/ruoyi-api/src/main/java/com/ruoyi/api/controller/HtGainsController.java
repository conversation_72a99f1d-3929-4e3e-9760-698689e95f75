package com.ruoyi.api.controller;

import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.system.domain.vo.GainsVo;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.HtGains;
import com.ruoyi.system.service.IHtGainsService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 收益Controller
 *
 * <AUTHOR>
 * @date 2024-11-29
 */
@RestController
@RequestMapping("/api/gains")
public class HtGainsController extends BaseController {
    @Autowired
    private IHtGainsService htGainsService;

/**
 * 查询收益列表
 */
//@PreAuthorize("@ss.hasPermi('system:gains:list')")
@GetMapping("/list")
    public TableDataInfo list(HtGains htGains) {
        startPage();
        List<HtGains> list = htGainsService.selectHtGainsList(htGains);
        return getDataTable(list);
    }



    /**
     * 获取收益详细信息
     */
//    @PreAuthorize("@ss.hasPermi('system:gains:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(htGainsService.selectHtGainsById(id));
    }

    /**
     * 新增收益
     */
//    @PreAuthorize("@ss.hasPermi('system:gains:add')")
    @Log(title = "收益", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody HtGains htGains) {
        return toAjax(htGainsService.insertHtGains(htGains));
    }

    /**
     * 修改收益
     */
//    @PreAuthorize("@ss.hasPermi('system:gains:edit')")
//    @Log(title = "收益", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody HtGains htGains) {
        return toAjax(htGainsService.updateHtGains(htGains));
    }

    /**
     * 删除收益
     */
//    @PreAuthorize("@ss.hasPermi('system:gains:remove')")
//    @Log(title = "收益", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(htGainsService.deleteHtGainsByIds(ids));
    }

    @GetMapping("/gainsList/{userId}")
    public AjaxResult getList(@PathVariable Integer userId){
        Map<String, Object> list = htGainsService.getList(userId);
        return success(list);
    }


}
