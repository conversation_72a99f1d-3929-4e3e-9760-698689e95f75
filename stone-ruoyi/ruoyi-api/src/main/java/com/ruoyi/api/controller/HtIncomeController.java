package com.ruoyi.api.controller;

import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.HtIncome;
import com.ruoyi.system.service.IHtIncomeService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 收益记录Controller
 *
 * <AUTHOR>
 * @date 2024-12-04
 */
@RestController
@RequestMapping("/api/system/income")
public class HtIncomeController extends BaseController {
    @Autowired
    private IHtIncomeService htIncomeService;

    /**
     * 查询收益记录列表
     */
    @GetMapping("/list")
    public TableDataInfo list(HtIncome htIncome) {
        startPage();
        List<HtIncome> list = htIncomeService.selectHtIncomeList(htIncome);
        return getDataTable(list);
    }



    /**
     * 获取收益记录详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(htIncomeService.selectHtIncomeById(id));
    }

    /**
     * 新增收益记录
     */
    @PostMapping
    public AjaxResult add(@RequestBody HtIncome htIncome) {
        return toAjax(htIncomeService.insertHtIncome(htIncome));
    }

    /**
     * 修改收益记录
     */
    @PutMapping
    public AjaxResult edit(@RequestBody HtIncome htIncome) {
        return toAjax(htIncomeService.updateHtIncome(htIncome));
    }

    /**
     * 删除收益记录
     */
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(htIncomeService.deleteHtIncomeByIds(ids));
    }
    /**
     * 查询收益记录列表
     */
    @GetMapping("/selectByUserId/{userId}")
    public AjaxResult
    selectByUserId(@PathVariable Integer userId) {
        Map<String, Object> list = htIncomeService.selectByUserId(userId);
        return AjaxResult.success(list);
    }
}
