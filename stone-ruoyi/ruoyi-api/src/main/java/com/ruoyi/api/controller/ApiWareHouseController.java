package com.ruoyi.api.controller;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.system.domain.vo.TeamOrderVo;
import com.ruoyi.system.domain.vo.WareHouseVo;
import com.ruoyi.system.service.TeamService;
import com.ruoyi.system.service.WareHouseService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/11/28 17:43
 * @description: 仓库
 */
@RestController
@RequestMapping("/api/warehouse")
public class ApiWareHouseController {
    @Resource
    private WareHouseService wareHouseService;
    //买方
    @GetMapping("/BuyerList/{userId}")
    public AjaxResult getBuyerList(@PathVariable Integer userId){
        List<WareHouseVo> list =wareHouseService.getBuyerList(userId);
        return AjaxResult.success(list);
    }
    //平台进行确认收款列表
    @GetMapping("/SystemList/{userId}")
    public AjaxResult getList(@PathVariable Integer userId){
        List<WareHouseVo> list =wareHouseService.getSystemList(userId);
        return AjaxResult.success(list);
    }

    //卖方
    @GetMapping("/SellerList/{userId}")
    public AjaxResult getSellerList(@PathVariable Integer userId){
        List<WareHouseVo> list =wareHouseService.getSellerList(userId);
        return AjaxResult.success(list);
    }
}
