package com.ruoyi.api.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.HtMerchantCertification;
import com.ruoyi.system.service.IHtMerchantCertificationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 商家认证管理Controller
 *
 * <AUTHOR>
 * @date 2024-11-23
 */
@RestController
@RequestMapping("/api/system/authentication")
public class ApiHtMerchantCertificationController extends BaseController {
    @Autowired
    private IHtMerchantCertificationService htMerchantCertificationService;

/**
 * 查询商家认证管理列表
 */
@GetMapping("/list")
    public TableDataInfo list(HtMerchantCertification htMerchantCertification) {
        startPage();
        List<HtMerchantCertification> list = htMerchantCertificationService.selectHtMerchantCertificationList(htMerchantCertification);
        return getDataTable(list);
    }

    /**
     * 导出商家认证管理列表
     */
    @Log(title = "商家认证管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, HtMerchantCertification htMerchantCertification) {
        List<HtMerchantCertification> list = htMerchantCertificationService.selectHtMerchantCertificationList(htMerchantCertification);
        ExcelUtil<HtMerchantCertification> util = new ExcelUtil<HtMerchantCertification>(HtMerchantCertification. class);
        util.exportExcel(response, list, "商家认证管理数据");
    }

    /**
     * 获取商家认证管理详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(htMerchantCertificationService.selectHtMerchantCertificationById(id));
    }

    /**
     * 新增商家认证管理
     */
    @Log(title = "商家认证管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody HtMerchantCertification htMerchantCertification) {
        return toAjax(htMerchantCertificationService.insertHtMerchantCertification(htMerchantCertification));
    }

    /**
     * 修改商家认证管理
     */
    @Log(title = "商家认证管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody HtMerchantCertification htMerchantCertification) {
        return toAjax(htMerchantCertificationService.updateHtMerchantCertification(htMerchantCertification));
    }

    /**
     * 删除商家认证管理
     */
    @Log(title = "商家认证管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(htMerchantCertificationService.deleteHtMerchantCertificationByIds(ids));
    }
}
