package com.ruoyi.api.controller;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.HtGoodsActivity;
import com.ruoyi.system.service.IHtGoodsActivityService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.List;

/**
 * 商品活动管理Controller
 *
 * <AUTHOR>
 * @date 2024-11-21
 */
@RestController
@RequestMapping("/api/system/HtActivity")
public class ApiHtGoodsActivityController extends BaseController {
    @Autowired
    private IHtGoodsActivityService htGoodsActivityService;

    /**
     * 查询商品活动管理列表
     */
    @GetMapping("/list")
    public TableDataInfo list(HtGoodsActivity htGoodsActivity) {
        startPage();
        List<HtGoodsActivity> list = htGoodsActivityService.selectHtGoodsActivityList(htGoodsActivity);
        return getDataTable(list);
    }



    /**
     * 导出商品活动管理列表
     */
    @Log(title = "商品活动管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, HtGoodsActivity htGoodsActivity) {
        List<HtGoodsActivity> list = htGoodsActivityService.selectHtGoodsActivityList(htGoodsActivity);
        ExcelUtil<HtGoodsActivity> util = new ExcelUtil<HtGoodsActivity>(HtGoodsActivity. class);
        util.exportExcel(response, list, "商品活动管理数据");
    }

    /**
     * 获取商品活动管理详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(htGoodsActivityService.selectHtGoodsActivityById(id));
    }

    /**
     * 新增商品活动管理
     */
    @Log(title = "商品活动管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody HtGoodsActivity htGoodsActivity) {
        return toAjax(htGoodsActivityService.insertHtGoodsActivity(htGoodsActivity));
    }

    /**
     * 修改商品活动管理
     */
    @Log(title = "商品活动管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody HtGoodsActivity htGoodsActivity) {
        return toAjax(htGoodsActivityService.updateHtGoodsActivity(htGoodsActivity));
    }

    /**
     * 删除商品活动管理
     */
    @Log(title = "商品活动管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(htGoodsActivityService.deleteHtGoodsActivityByIds(ids));
    }
}
