package com.ruoyi.api.controller;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.system.domain.vo.DistributionCentresVo;
import com.ruoyi.system.service.DistributionCentreService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/11/25 16:31
 * @description: 分销中心
 */
@RestController
@RequestMapping("/api/distribution")
public class DistributionCentresController {

    @Resource
    private DistributionCentreService distributionCentreService;

    @GetMapping("/list/{userId}")
    public AjaxResult getDistributionCentreList(@PathVariable Integer userId){
        List<DistributionCentresVo> list = distributionCentreService.getDistributionCentreList(userId);
        return AjaxResult.success(list);
    }
}
