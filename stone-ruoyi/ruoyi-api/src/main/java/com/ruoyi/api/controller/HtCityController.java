package com.ruoyi.api.controller;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.system.domain.HtCity;
import com.ruoyi.system.service.IHtCityService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 城市管理Controller
 * 
 * <AUTHOR>
 * @date 2024-11-20
 */
@RestController
@RequestMapping("/api/city")
public class HtCityController extends BaseController
{
    @Autowired
    private IHtCityService htCityService;

    /**
     * 查询城市管理列表
     */
    @GetMapping("/list")
    public TableDataInfo list(HtCity htCity)
    {
        List<HtCity> list = htCityService.selectHtCityList(htCity);
        return getDataTable(list);
    }



    /**
     * 获取城市管理详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(htCityService.selectHtCityById(id));
    }

    /**
     * 新增城市管理
     */
    @PostMapping
    public AjaxResult add(@RequestBody HtCity htCity)
    {
        return toAjax(htCityService.insertHtCity(htCity));
    }

    /**
     * 修改城市管理
     */
    @PutMapping
    public AjaxResult edit(@RequestBody HtCity htCity)
    {
        return toAjax(htCityService.updateHtCity(htCity));
    }


    /**
     * 修改城市管理
     */
    @GetMapping("/testOrderShare")
    @Anonymous
    public AjaxResult testOrderShare()
    {
        return toAjax(htCityService.testOrderShare());
    }

    /**
     * 修改城市管理
     */
    @GetMapping("/testOrderPay")
    @Anonymous
    public AjaxResult testOrderPay()
    {
        return toAjax(htCityService.testOrderPay());
    }

    /**
     * 删除城市管理
     */
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(htCityService.deleteHtCityByIds(ids));
    }
}
