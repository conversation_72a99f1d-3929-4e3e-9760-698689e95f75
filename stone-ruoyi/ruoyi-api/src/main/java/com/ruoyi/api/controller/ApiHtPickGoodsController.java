package com.ruoyi.api.controller;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.HtPickGoods;
import com.ruoyi.system.service.IHtPickGoodsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 用户提货管理Controller
 *
 * <AUTHOR>
 * @date 2024-12-09
 */
@RestController
@RequestMapping("/api/system/pick")
public class ApiHtPickGoodsController extends BaseController {
    @Autowired
    private IHtPickGoodsService htPickGoodsService;

/**
 * 查询用户提货管理列表
 */
@GetMapping("/list")
    public TableDataInfo list(HtPickGoods htPickGoods) {
        startPage();
        List<HtPickGoods> list = htPickGoodsService.selectHtPickGoodsList(htPickGoods);
        return getDataTable(list);
    }

    /**
     * 导出用户提货管理列表
     */
    @Log(title = "用户提货管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, HtPickGoods htPickGoods) {
        List<HtPickGoods> list = htPickGoodsService.selectHtPickGoodsList(htPickGoods);
        ExcelUtil<HtPickGoods> util = new ExcelUtil<HtPickGoods>(HtPickGoods. class);
        util.exportExcel(response, list, "用户提货管理数据");
    }

    /**
     * 获取用户提货管理详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(htPickGoodsService.selectHtPickGoodsById(id));
    }

    /**
     * 新增用户提货管理
     */
    @Log(title = "用户提货管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody HtPickGoods htPickGoods) {
        return toAjax(htPickGoodsService.insertHtPickGoods(htPickGoods));
    }

    /**
     * 修改用户提货管理
     */
    @Log(title = "用户提货管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody HtPickGoods htPickGoods) {
        return toAjax(htPickGoodsService.updateHtPickGoods(htPickGoods));
    }

    /**
     * 删除用户提货管理
     */
    @Log(title = "用户提货管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(htPickGoodsService.deleteHtPickGoodsByIds(ids));
    }
}
