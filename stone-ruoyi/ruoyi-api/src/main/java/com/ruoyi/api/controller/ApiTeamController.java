package com.ruoyi.api.controller;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.system.domain.vo.TeamOrderVo;
import com.ruoyi.system.service.TeamService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/11/25 14:31
 * @description:团队订单
 */
@RestController
@RequestMapping("/api/team")
public class ApiTeamController {
    @Resource
    private TeamService teamService;
    @GetMapping("/list/{myId}")
    public AjaxResult getTeamOrderList(@PathVariable Integer myId){
        List<TeamOrderVo> list =teamService.getTeamOrderList(myId);
        return AjaxResult.success(list);
    }
}
