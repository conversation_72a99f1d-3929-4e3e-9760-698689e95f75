package com.ruoyi.api.controller;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.system.domain.HtPaymentAgreements;
import com.ruoyi.system.service.IHtPaymentAgreementsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

/**
 * 签约Controller
 *
 * <AUTHOR>
 * @date 2024-11-22
 */
@RestController
@RequestMapping("/api/agreements")
public class HtPaymentAgreementsController extends BaseController {
    @Autowired
    private IHtPaymentAgreementsService htPaymentAgreementsService;

    /**
     * 查询签约列表
     */
    @Anonymous
    @GetMapping("/list")
    public TableDataInfo list(HtPaymentAgreements htPaymentAgreements) {
        startPage();
        List<HtPaymentAgreements> list = htPaymentAgreementsService.selectHtPaymentAgreementsList(htPaymentAgreements);
        return getDataTable(list);
    }



    /**
     * 获取签约详细信息
     */
    @Anonymous
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(htPaymentAgreementsService.selectHtPaymentAgreementsById(id));
    }

    /**
     * 新增签约
     */
    @Anonymous
    @PostMapping
    public AjaxResult add(@RequestBody HtPaymentAgreements htPaymentAgreements) {
        htPaymentAgreements.setSignedAt(new Date());
        return toAjax(htPaymentAgreementsService.insertHtPaymentAgreements(htPaymentAgreements));
    }

    /**
     * 修改签约
     */
    @PutMapping
    @Anonymous
    public AjaxResult edit(@RequestBody HtPaymentAgreements htPaymentAgreements) {
        htPaymentAgreements.setUpdateAt(new Date());
        return toAjax(htPaymentAgreementsService.updateHtPaymentAgreements(htPaymentAgreements));
    }

    /**
     * 删除签约
     */
    @Anonymous
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(htPaymentAgreementsService.deleteHtPaymentAgreementsByIds(ids));
    }
}
