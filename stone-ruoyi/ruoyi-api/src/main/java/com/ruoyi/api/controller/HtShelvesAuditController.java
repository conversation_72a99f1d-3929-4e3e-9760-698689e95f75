package com.ruoyi.api.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.system.domain.vo.HtShelvesAuditDto;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.HtShelvesAudit;
import com.ruoyi.system.service.IHtShelvesAuditService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 石头委托审核Controller
 *
 * <AUTHOR>
 * @date 2024-12-05
 */
@RestController
@RequestMapping("/api/system/audit")
public class HtShelvesAuditController extends BaseController {
    @Autowired
    private IHtShelvesAuditService htShelvesAuditService;

    /**
     * 查询石头委托审核列表
     */
    @GetMapping("/list")
    public TableDataInfo list(HtShelvesAudit htShelvesAudit) {
        startPage();
        List<HtShelvesAudit> list = htShelvesAuditService.selectHtShelvesAuditList(htShelvesAudit);
        return getDataTable(list);
    }



    /**
     * 获取石头委托审核详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(htShelvesAuditService.selectHtShelvesAuditById(id));
    }

    /**
     * 新增石头委托审核
     */
    @PostMapping
    public AjaxResult add(@RequestBody HtShelvesAudit htShelvesAudit) {
        return toAjax(htShelvesAuditService.insertHtShelvesAudit(htShelvesAudit));
    }

    /**
     * 修改石头委托审核
     */
    @PutMapping
    public AjaxResult edit(@RequestBody HtShelvesAudit htShelvesAudit) {
        return toAjax(htShelvesAuditService.updateHtShelvesAudit(htShelvesAudit));
    }

    /**
     * 删除石头委托审核
     */
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(htShelvesAuditService.deleteHtShelvesAuditByIds(ids));
    }

    /**
     * 委托寄售发布
     */
    @PostMapping("/submit")
    public AjaxResult submit(@RequestBody HtShelvesAuditDto htShelvesAudit){

        return toAjax(htShelvesAuditService.submit(htShelvesAudit));
    }

}
