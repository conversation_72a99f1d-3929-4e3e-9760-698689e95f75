package com.ruoyi.api.controller;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.domain.AppUser;
import com.ruoyi.system.domain.HtUserShares;
import com.ruoyi.system.mapper.AppUserMapper;
import com.ruoyi.system.mapper.HtUserSharesMapper;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.framework.web.service.TokenService;
import com.ruoyi.system.service.IAppUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @Date 2024/11/22 17:07
 * @description:
 */
@RestController
@RequestMapping("/api/QRcode")
public class QRcodeController extends BaseController {

    @Autowired
    private TokenService tokenService;
    @Autowired
    private IAppUserService appUserService;

    @Autowired
    private HtUserSharesMapper htUserSharesMapper;

    @Autowired
    private AppUserMapper appUserMapper;
    /**
     * 邀请
     *
     * @return 结果
     */
    @PostMapping("/register")
    public AjaxResult register(@RequestBody AppUser appUser) {
        AppUser insert = new AppUser();
        insert.setPhone(appUser.getPhone());
        insert.setPassword("xxxxxx");
        insert.setNickName("普通用户");
        insert.setHeadImg("");

        int resCount = appUserMapper.insert(insert);

        Long id = insert.getId();
        HtUserShares htUserShares = new HtUserShares();
        htUserShares.setUserId(id);
        Long userId = SecurityUtils.getUserId();
        htUserShares.setMyId(userId);
        return toAjax(htUserSharesMapper.insertHtUserShares(htUserShares));

    }
}
