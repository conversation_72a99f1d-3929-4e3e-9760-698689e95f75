package com.ruoyi.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.HtUserShares;
import com.ruoyi.system.service.IHtUserSharesService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 分享主Controller
 *
 * <AUTHOR>
 * @date 2024-11-22
 */
@RestController
@RequestMapping("/system/shares")
public class HtUserSharesController extends BaseController {
    @Autowired
    private IHtUserSharesService htUserSharesService;

/**
 * 查询分享主列表
 */
@PreAuthorize("@ss.hasPermi('system:shares:list')")
@GetMapping("/list")
    public TableDataInfo list(HtUserShares htUserShares) {
        startPage();
        List<HtUserShares> list = htUserSharesService.selectHtUserSharesList(htUserShares);
        return getDataTable(list);
    }

    /**
     * 导出分享主列表
     */
    @PreAuthorize("@ss.hasPermi('system:shares:export')")
    @Log(title = "分享主", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, HtUserShares htUserShares) {
        List<HtUserShares> list = htUserSharesService.selectHtUserSharesList(htUserShares);
        ExcelUtil<HtUserShares> util = new ExcelUtil<HtUserShares>(HtUserShares. class);
        util.exportExcel(response, list, "分享主数据");
    }

    /**
     * 获取分享主详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:shares:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(htUserSharesService.selectHtUserSharesById(id));
    }

    /**
     * 新增分享主
     */
    @PreAuthorize("@ss.hasPermi('system:shares:add')")
    @Log(title = "分享主", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody HtUserShares htUserShares) {
        return toAjax(htUserSharesService.insertHtUserShares(htUserShares));
    }

    /**
     * 修改分享主
     */
    @PreAuthorize("@ss.hasPermi('system:shares:edit')")
    @Log(title = "分享主", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody HtUserShares htUserShares) {
        return toAjax(htUserSharesService.updateHtUserShares(htUserShares));
    }

    /**
     * 删除分享主
     */
    @PreAuthorize("@ss.hasPermi('system:shares:remove')")
    @Log(title = "分享主", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(htUserSharesService.deleteHtUserSharesByIds(ids));
    }
}
