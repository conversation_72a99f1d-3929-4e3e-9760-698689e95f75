package com.ruoyi.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.utils.SecurityUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.HtPaymentAgreements;
import com.ruoyi.system.service.IHtPaymentAgreementsService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 签约Controller
 *
 * <AUTHOR>
 * @date 2024-11-27
 */
@RestController
@RequestMapping("/system/agreements")
public class HtPaymentAgreementsController extends BaseController {
    @Autowired
    private IHtPaymentAgreementsService htPaymentAgreementsService;

    /**
     * 查询用户签约列表
     */
    @PreAuthorize("@ss.hasPermi('system:agreements:list')")
    @GetMapping("/list")
    public TableDataInfo list(HtPaymentAgreements htPaymentAgreements) {
        startPage();
        htPaymentAgreements.setUserId(1L);
        List<HtPaymentAgreements> list = htPaymentAgreementsService.selectHtPaymentAgreementsListNot(htPaymentAgreements);
        return getDataTable(list);
    }

    /**
     * 查询平台签约列表
     */
    @PreAuthorize("@ss.hasPermi('system:agreements:list')")
    @GetMapping("/sysList")
    public TableDataInfo sysList(HtPaymentAgreements htPaymentAgreements) {
        startPage();
        htPaymentAgreements.setUserId(1L);
        List<HtPaymentAgreements> list = htPaymentAgreementsService.selectHtPaymentAgreementsList(htPaymentAgreements);
        return getDataTable(list);
    }


    /**
     * 导出签约列表
     */
    @PreAuthorize("@ss.hasPermi('system:agreements:export')")
    @Log(title = "签约", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, HtPaymentAgreements htPaymentAgreements) {
        List<HtPaymentAgreements> list = htPaymentAgreementsService.selectHtPaymentAgreementsList(htPaymentAgreements);
        ExcelUtil<HtPaymentAgreements> util = new ExcelUtil<HtPaymentAgreements>(HtPaymentAgreements. class);
        util.exportExcel(response, list, "签约数据");
    }

    /**
     * 获取签约详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:agreements:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(htPaymentAgreementsService.selectHtPaymentAgreementsById(id));
    }

    /**
     * 新增签约
     */
    @PreAuthorize("@ss.hasPermi('system:agreements:add')")
    @Log(title = "签约", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody HtPaymentAgreements htPaymentAgreements) {
        htPaymentAgreements.setUserId(SecurityUtils.getUserId());
        return toAjax(htPaymentAgreementsService.insertHtPaymentAgreements(htPaymentAgreements));
    }

    /**
     * 修改签约
     */
    @PreAuthorize("@ss.hasPermi('system:agreements:edit')")
    @Log(title = "签约", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody HtPaymentAgreements htPaymentAgreements) {
        return toAjax(htPaymentAgreementsService.updateHtPaymentAgreements(htPaymentAgreements));
    }

    /**
     * 删除签约
     */
    @PreAuthorize("@ss.hasPermi('system:agreements:remove')")
    @Log(title = "签约", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(htPaymentAgreementsService.deleteHtPaymentAgreementsByIds(ids));
    }
}
