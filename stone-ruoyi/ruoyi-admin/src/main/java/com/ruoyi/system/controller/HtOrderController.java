package com.ruoyi.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.system.domain.AppUser;
import com.ruoyi.system.domain.HtGoods;
import com.ruoyi.system.domain.HtOrderRecord;
import com.ruoyi.system.domain.vo.HtGoodsItem;
import com.ruoyi.system.domain.vo.HtOrderSystemVo;
import com.ruoyi.system.mapper.HtGoodsMapper;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.HtOrder;
import com.ruoyi.system.service.IHtOrderService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 订单Controller
 *
 * <AUTHOR>
 * @date 2024-11-23
 */
@RestController
@RequestMapping("/system/order")
public class HtOrderController extends BaseController {
    @Autowired
    private IHtOrderService htOrderService;

    @Autowired
    private HtGoodsMapper htGoodsMapper;

    /**
     * 查询抢购明细列表
     */
//    @PreAuthorize("@ss.hasPermi('system:order:list')")
    @Anonymous
    @GetMapping("/grab/list")
            public AjaxResult grabList(@RequestParam(defaultValue = "1") Integer pageNum, @RequestParam(defaultValue = "10") Integer pageSize, HtOrder htOrder ) {
        PageHelper.startPage(pageNum, pageSize);
        List<HtGoodsItem> list = htOrderService.selectGrabOrderList(htOrder);
        PageInfo<HtGoodsItem> pageInfo = new PageInfo<>(list);

        pageInfo.setTotal(htGoodsMapper.selectCount(Wrappers.<HtGoods>query().ne("id", 0).eq("is_best",1).eq("is_new",1).eq("is_del",0)));
        return AjaxResult.success(pageInfo);
    }

    @Anonymous
    @GetMapping("/grab/user")
    public AjaxResult grabUser(@RequestParam(defaultValue = "1") Integer pageNum, @RequestParam(defaultValue = "10") Integer pageSize){

            List<AppUser> list = htOrderService.grabUser();

        return AjaxResult.success(list);
    }


    /**
     * 查询订单列表
     */
    @PreAuthorize("@ss.hasPermi('system:order:list')")
    @GetMapping("/list")
    public TableDataInfo list(HtOrder htOrder) {
        startPage();
        List<HtOrderSystemVo> list = htOrderService.selectSysHtOrderList(htOrder);
        return getDataTable(list);
    }

    /**
     * 导出订单列表
     */
    @PreAuthorize("@ss.hasPermi('system:order:export')")
    @Log(title = "订单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, HtOrder htOrder) {
        List<HtOrder> list = htOrderService.selectHtOrderList(htOrder);
        ExcelUtil<HtOrder> util = new ExcelUtil<HtOrder>(HtOrder.class);
        util.exportExcel(response, list, "订单数据");
    }

    /**
     * 获取订单详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:order:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(htOrderService.selectHtOrderById(id));
    }

    /**
     * 新增订单
     */
    @PreAuthorize("@ss.hasPermi('system:order:add')")
    @Log(title = "订单", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody HtOrder htOrder) {
        return toAjax(htOrderService.insertHtOrder(htOrder));
    }

    /**
     * 修改订单
     */
    @PreAuthorize("@ss.hasPermi('system:order:edit')")
    @Log(title = "订单", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody HtOrder htOrder) {
        return toAjax(htOrderService.updateHtOrder(htOrder));
    }

    /**
     * 删除订单
     */
    @PreAuthorize("@ss.hasPermi('system:order:remove')")
    @Log(title = "订单", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(htOrderService.deleteHtOrderByIds(ids));
    }
}
