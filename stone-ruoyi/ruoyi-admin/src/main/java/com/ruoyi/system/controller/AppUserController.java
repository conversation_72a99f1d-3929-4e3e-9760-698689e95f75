package com.ruoyi.system.controller;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.AppUser;
import com.ruoyi.system.service.IAppUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * app用户Controller
 *
 * <AUTHOR>
 * @date 2024-11-27
 */
@RestController
@RequestMapping("/system/appuser")
public class AppUserController extends BaseController {
    @Autowired
    private IAppUserService appUserService;

    /**
     * 查询app用户列表
     */
    @PreAuthorize("@ss.hasPermi('system:appuser:list')")
    @GetMapping("/list")
    public TableDataInfo list(AppUser appUser) {
        startPage();
        List<AppUser> list = appUserService.selectAppUserList(appUser);
        return getDataTable(list);
    }

    /**
     * 导出app用户列表
     */
    @PreAuthorize("@ss.hasPermi('system:appuser:export')")
    @Log(title = "app用户", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, AppUser appUser) {
        List<AppUser> list = appUserService.selectAppUserList(appUser);
        ExcelUtil<AppUser> util = new ExcelUtil<AppUser>(AppUser.class);
        util.exportExcel(response, list, "app用户数据");
    }

    /**
     * 获取app用户详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:appuser:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(appUserService.selectAppUserById(id));
    }

    /**
     * 新增app用户
     */
    @PreAuthorize("@ss.hasPermi('system:appuser:add')")
    @Log(title = "app用户", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody AppUser appUser) {
        return toAjax(appUserService.insertAppUser(appUser));
    }

    /**
     * 修改app用户
     */
    @PreAuthorize("@ss.hasPermi('system:appuser:edit')")
    @Log(title = "app用户", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody AppUser appUser) {
        return toAjax(appUserService.updateAppUser(appUser));
    }

    /**
     * 删除app用户
     */
    @PreAuthorize("@ss.hasPermi('system:appuser:remove')")
    @Log(title = "app用户", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(appUserService.deleteAppUserByIds(ids));
    }

    @PreAuthorize("@ss.hasPermi('system:appuser:export')")
    @Anonymous
    @GetMapping("/selectAppUserPaymentsByUserId/{userId}")
    public AjaxResult selectAppUserPaymentsByUserId(@PathVariable("userId") Long userId) {
        return AjaxResult.success(appUserService.selectAppUserPaymentsByUserId(userId));
    }

    /**
     * 获取用户下拉列表
     */
    @GetMapping("/selectUserList")
    public AjaxResult selectUserList() {
        List<AppUser> list = appUserService.selectAppUserList(new AppUser());
        return AjaxResult.success(list);
    }
}
