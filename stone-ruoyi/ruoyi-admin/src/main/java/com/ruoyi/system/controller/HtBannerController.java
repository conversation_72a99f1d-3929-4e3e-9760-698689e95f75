package com.ruoyi.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.HtBanner;
import com.ruoyi.system.service.IHtBannerService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * bannerController
 *
 * <AUTHOR>
 * @date 2024-11-21
 */
@RestController
@RequestMapping("/system/banner")
public class HtBannerController extends BaseController {
    @Autowired
    private IHtBannerService htBannerService;

/**
 * 查询banner列表
 */
@PreAuthorize("@ss.hasPermi('system:banner:list')")
@GetMapping("/list")
    public TableDataInfo list(HtBanner htBanner) {
        startPage();
        List<HtBanner> list = htBannerService.selectHtBannerList(htBanner);
        return getDataTable(list);
    }

    /**
     * 导出banner列表
     */
    @PreAuthorize("@ss.hasPermi('system:banner:export')")
    @Log(title = "banner", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, HtBanner htBanner) {
        List<HtBanner> list = htBannerService.selectHtBannerList(htBanner);
        ExcelUtil<HtBanner> util = new ExcelUtil<HtBanner>(HtBanner. class);
        util.exportExcel(response, list, "banner数据");
    }

    /**
     * 获取banner详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:banner:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(htBannerService.selectHtBannerById(id));
    }

    /**
     * 新增banner
     */
    @PreAuthorize("@ss.hasPermi('system:banner:add')")
    @Log(title = "banner", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody HtBanner htBanner) {
        return toAjax(htBannerService.insertHtBanner(htBanner));
    }

    /**
     * 修改banner
     */
    @PreAuthorize("@ss.hasPermi('system:banner:edit')")
    @Log(title = "banner", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody HtBanner htBanner) {
        return toAjax(htBannerService.updateHtBanner(htBanner));
    }

    /**
     * 删除banner
     */
    @PreAuthorize("@ss.hasPermi('system:banner:remove')")
    @Log(title = "banner", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(htBannerService.deleteHtBannerByIds(ids));
    }
}
