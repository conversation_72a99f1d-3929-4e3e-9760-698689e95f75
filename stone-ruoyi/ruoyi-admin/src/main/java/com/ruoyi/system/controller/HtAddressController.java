package com.ruoyi.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.HtAddress;
import com.ruoyi.system.service.IHtAddressService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 地址Controller
 *
 * <AUTHOR>
 * @date 2024-11-26
 */
@RestController
@RequestMapping("/system/address")
public class HtAddressController extends BaseController {
    @Autowired
    private IHtAddressService htAddressService;

/**
 * 查询地址列表
 */
@PreAuthorize("@ss.hasPermi('system:address:list')")
@GetMapping("/list")
    public TableDataInfo list(HtAddress htAddress) {
        startPage();
        List<HtAddress> list = htAddressService.selectHtAddressList(htAddress);
        return getDataTable(list);
    }

    /**
     * 导出地址列表
     */
    @PreAuthorize("@ss.hasPermi('system:address:export')")
    @Log(title = "地址", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, HtAddress htAddress) {
        List<HtAddress> list = htAddressService.selectHtAddressList(htAddress);
        ExcelUtil<HtAddress> util = new ExcelUtil<HtAddress>(HtAddress. class);
        util.exportExcel(response, list, "地址数据");
    }

    /**
     * 获取地址详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:address:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(htAddressService.selectHtAddressById(id));
    }

    /**
     * 新增地址
     */
    @PreAuthorize("@ss.hasPermi('system:address:add')")
    @Log(title = "地址", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody HtAddress htAddress) {
        return toAjax(htAddressService.insertHtAddress(htAddress));
    }

    /**
     * 修改地址
     */
    @PreAuthorize("@ss.hasPermi('system:address:edit')")
    @Log(title = "地址", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody HtAddress htAddress) {
        return toAjax(htAddressService.updateHtAddress(htAddress));
    }

    /**
     * 删除地址
     */
    @PreAuthorize("@ss.hasPermi('system:address:remove')")
    @Log(title = "地址", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(htAddressService.deleteHtAddressByIds(ids));
    }
}
