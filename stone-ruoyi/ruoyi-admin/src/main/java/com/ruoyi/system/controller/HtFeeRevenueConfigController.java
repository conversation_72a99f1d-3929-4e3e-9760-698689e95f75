package com.ruoyi.system.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.HtFeeRevenueConfig;
import com.ruoyi.system.service.IHtFeeRevenueConfigService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 上架费 收益配置Controller
 *
 * <AUTHOR>
 * @date 2024-12-13
 */
@RestController
@RequestMapping("/system/computing/config")
public class HtFeeRevenueConfigController extends BaseController {
    @Autowired
    private IHtFeeRevenueConfigService htFeeRevenueConfigService;

/**
 * 查询上架费 收益配置列表
 */
@PreAuthorize("@ss.hasPermi('system:config:list')")
@GetMapping("/list")
    public TableDataInfo list(HtFeeRevenueConfig htFeeRevenueConfig) {
        startPage();
        List<HtFeeRevenueConfig> list = htFeeRevenueConfigService.selectHtFeeRevenueConfigList(htFeeRevenueConfig);
        return getDataTable(list);
    }

    /**
     * 导出上架费 收益配置列表
     */
    @PreAuthorize("@ss.hasPermi('system:config:export')")
    @Log(title = "上架费 收益配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, HtFeeRevenueConfig htFeeRevenueConfig) {
        List<HtFeeRevenueConfig> list = htFeeRevenueConfigService.selectHtFeeRevenueConfigList(htFeeRevenueConfig);
        ExcelUtil<HtFeeRevenueConfig> util = new ExcelUtil<HtFeeRevenueConfig>(HtFeeRevenueConfig. class);
        util.exportExcel(response, list, "上架费 收益配置数据");
    }

    /**
     * 获取上架费 收益配置详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:config:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(htFeeRevenueConfigService.selectHtFeeRevenueConfigById(id));
    }

    /**
     * 新增上架费 收益配置
     */
    @PreAuthorize("@ss.hasPermi('system:config:add')")
    @Log(title = "上架费 收益配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody HtFeeRevenueConfig htFeeRevenueConfig) {
        return toAjax(htFeeRevenueConfigService.insertHtFeeRevenueConfig(htFeeRevenueConfig));
    }

    /**
     * 修改上架费 收益配置
     */
    @PreAuthorize("@ss.hasPermi('system:config:edit')")
    @Log(title = "上架费 收益配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody HtFeeRevenueConfig htFeeRevenueConfig) {
        return toAjax(htFeeRevenueConfigService.updateHtFeeRevenueConfig(htFeeRevenueConfig));
    }

    /**
     * 删除上架费 收益配置
     */
    @PreAuthorize("@ss.hasPermi('system:config:remove')")
    @Log(title = "上架费 收益配置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(htFeeRevenueConfigService.deleteHtFeeRevenueConfigByIds(ids));
    }
}
