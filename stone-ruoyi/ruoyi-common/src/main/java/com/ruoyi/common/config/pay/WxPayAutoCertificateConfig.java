// package com.ruoyi.common.config.pay;
//
// import com.wechat.pay.java.core.Config;
// import com.wechat.pay.java.core.RSAAutoCertificateConfig;
// import com.wechat.pay.java.core.notification.NotificationConfig;
// import com.wechat.pay.java.core.notification.NotificationParser;
// import com.wechat.pay.java.service.partnerpayments.h5.H5Service;
// import com.wechat.pay.java.service.partnerpayments.jsapi.JsapiService;
// import com.wechat.pay.java.service.payments.nativepay.NativePayService;
// import org.springframework.context.annotation.Bean;
// import org.springframework.context.annotation.Configuration;
// import org.springframework.context.annotation.Primary;
//
// import javax.annotation.PostConstruct;
// import javax.annotation.Resource;
//
// /**
//  * <AUTHOR>
//  * @ClassName WxPayAutoCertificateConfig
//  * @description: 微信支付证书自动更新配置
//  * @date 2024年01月03日
//  * @version: 1.0
//  */
// @Configuration
// public class WxPayAutoCertificateConfig {
//
//     @Resource
//     private WxPayConfig wxPayConfig;
//
//     private Config config;
//
//     @PostConstruct
//     public Config initConfig() {
//         // 使用自动更新平台证书的RSA配置
//         // 一个商户号只能初始化一个配置，否则会因为重复的下载任务报错
//         config = new RSAAutoCertificateConfig.Builder()
//                 .merchantId(wxPayConfig.getMerchantId())
//                 .privateKeyFromPath(wxPayConfig.getPrivateKey())
//                 .merchantSerialNumber(wxPayConfig.getMerchantSerialNumber())
//                 .apiV3Key(wxPayConfig.getApiV3Key())
//                 .build();
//         return config;
//     }
//
//     @Primary
//     @Bean()
//     public H5Service h5Service() {
//         return new H5Service.Builder()
//                 .config(config)
//                 .build();
//     }
//
//     @Primary
//     @Bean()
//     public JsapiService jsapiService() {
//         return new JsapiService.Builder()
//                 .config(config)
//                 .build();
//     }
//
//     @Primary
//     @Bean()
//     public NativePayService nativePayService() {
//         return new NativePayService.Builder()
//                 .config(config)
//                 .build();
//     }
//
//     @Primary
//     @Bean
//     public NotificationParser notificationParser() {
//         return new NotificationParser((NotificationConfig) config);
//     }
// }